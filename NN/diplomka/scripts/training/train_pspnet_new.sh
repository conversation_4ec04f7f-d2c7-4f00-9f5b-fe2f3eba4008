#!/bin/bash

# Nový PSPNet trénink s vylepšenou architekturou
# Používá novou implementaci PSPNet s ResNet101 backbone a lepší stabilitu

echo "=== Spouštím trénink nové PSPNet architektury ==="
echo "Datum: $(date)"
echo "GPU status:"
nvidia-smi --query-gpu=index,name,memory.used,memory.total,utilization.gpu --format=csv

# Nastavení prostředí
export CUDA_VISIBLE_DEVICES=0,1
export PYTHONPATH="/home/<USER>/SpheroSeg/NN/diplomka:$PYTHONPATH"

# Parametry tréninku
MODEL_NAME="pspnet"
BATCH_SIZE=2  # Menší batch size kvůli ResNet101 backbone
LEARNING_RATE=0.0001
EPOCHS=50
IMG_SIZE=1024

# Cesty
DATA_DIR="/data/prusek/training_small"
OUTPUT_DIR="./scripts/training/outputs/pspnet_new_$(date +%Y%m%d_%H%M%S)"
LOG_FILE="./scripts/training/logs/pspnet_new_train_$(date +%Y%m%d_%H%M%S).log"

# Vytvoření výstupních adresářů
mkdir -p "$(dirname "$OUTPUT_DIR")"
mkdir -p "$(dirname "$LOG_FILE")"

echo "Výstupní adresář: $OUTPUT_DIR"
echo "Log soubor: $LOG_FILE"
echo ""

# Spuštění tréninku
python3 CNN_main_spheroid.py \
    --model "$MODEL_NAME" \
    --data_dir "$DATA_DIR" \
    --output_dir "$OUTPUT_DIR" \
    --batch_size $BATCH_SIZE \
    --learning_rate $LEARNING_RATE \
    --epochs $EPOCHS \
    --img_size $IMG_SIZE \
    --scheduler "reduce" \
    --patience 10 \
    --min_lr 1e-7 \
    --weight_decay 1e-4 \
    --save_every 5 \
    --validate_every 1 \
    --num_workers 4 \
    --mixed_precision \
    --use_instance_norm \
    --augment_prob 0.8 \
    --warmup_epochs 3 \
    --gradient_clip_val 1.0 \
    2>&1 | tee "$LOG_FILE"

echo ""
echo "=== Trénink dokončen ==="
echo "Výsledky uloženy v: $OUTPUT_DIR"
echo "Log soubor: $LOG_FILE"
echo "Datum dokončení: $(date)"

# Zobrazení finálních GPU statistik
echo ""
echo "Finální GPU status:"
nvidia-smi --query-gpu=index,name,memory.used,memory.total,utilization.gpu --format=csv
