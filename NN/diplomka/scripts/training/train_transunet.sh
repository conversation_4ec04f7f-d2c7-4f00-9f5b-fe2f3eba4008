#!/bin/bash
# Training script for TransUNet on spheroid dataset with 2x NVIDIA L40S

# TransUNet training with Vision Transformer backbone
python ../../CNN_main_spheroid.py \
    --dataset_path /data/prusek/training_big \
    --output_dir ./outputs/transunet_spheroid \
    --model transunet \
    --pretrained \
    --epochs 100 \
    --batch_size 6 \
    --lr 1e-3 \
    --weight_decay 1e-4 \
    --img_size 1024 \
    --optimizer adamw \
    --scheduler cosine \
    --num_workers 8 \
    --patience 20 \
    --gpus 2 \
    --focal_weight 0.8 \
    --dice_weight 1.2 \
    --iou_weight 0.6 \
    --use_instance_norm \
    --use_tta