#!/bin/bash

# Train Advanced ResUNet with modern attention mechanisms
# Features: SimAM + Triplet Attention + Lightweight Self-Attention

echo "=========================================="
echo "Training Advanced ResUNet with Modern Attention"
echo "=========================================="

# Set environment
export CUDA_VISIBLE_DEVICES=0,1
# Go to project root directory
cd ../..
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Training parameters
DATASET_PATH="/home/<USER>/spheroid_data/training_1024_all"
BATCH_SIZE=8  # Reduced due to more complex architecture
EPOCHS=100
PATIENCE=20
MIN_DELTA=1e-4

# Model specific parameters
MODEL_NAME="resunet_advanced"
LEARNING_RATE=5e-4  # Between ResUNet (1e-3) and HRNet (5e-4)
OPTIMIZER="adamw"

# Create output directory
OUTPUT_DIR="scripts/training/outputs/${MODEL_NAME}_$(date +%Y%m%d_%H%M%S)"
mkdir -p $OUTPUT_DIR

# Log file
LOG_FILE="${OUTPUT_DIR}/training.log"

echo "Configuration:" | tee $LOG_FILE
echo "- Model: Advanced ResUNet" | tee -a $LOG_FILE
echo "- Dataset: $DATASET_PATH" | tee -a $LOG_FILE
echo "- Batch Size: $BATCH_SIZE" | tee -a $LOG_FILE
echo "- Learning Rate: $LEARNING_RATE" | tee -a $LOG_FILE
echo "- Optimizer: $OPTIMIZER" | tee -a $LOG_FILE
echo "- Output Directory: $OUTPUT_DIR" | tee -a $LOG_FILE
echo "" | tee -a $LOG_FILE

# First, check if model loads correctly and has expected capacity
echo "Checking model capacity..." | tee -a $LOG_FILE
python -c "
import sys
sys.path.append('.')
from models.resunet_advanced import AdvancedResUNet
import torch

model = AdvancedResUNet(in_channels=3, out_channels=1, use_instance_norm=True)
total_params = sum(p.numel() for p in model.parameters())
print(f'Total parameters: {total_params:,}')
print(f'Model size: {total_params * 4 / 1024 / 1024:.2f} MB')

# Test forward pass
x = torch.randn(1, 3, 256, 256)
try:
    y = model(x)
    print(f'Input shape: {x.shape}')
    print(f'Output shape: {y.shape}')
    print('Model loaded successfully!')
except Exception as e:
    print(f'Error: {e}')
    sys.exit(1)
" | tee -a $LOG_FILE

if [ ${PIPESTATUS[0]} -ne 0 ]; then
    echo "Model check failed!" | tee -a $LOG_FILE
    exit 1
fi

echo "" | tee -a $LOG_FILE
echo "Starting training..." | tee -a $LOG_FILE

# Run training with recommended parameters
python CNN_main_spheroid.py \
    --dataset_path $DATASET_PATH \
    --model resunet_advanced \
    --batch_size $BATCH_SIZE \
    --epochs $EPOCHS \
    --lr $LEARNING_RATE \
    --optimizer $OPTIMIZER \
    --patience $PATIENCE \
    --min_delta $MIN_DELTA \
    --use_instance_norm \
    --gpus 2 \
    --output_dir $OUTPUT_DIR \
    --boundary_weight 0.1 \
    2>&1 | tee -a $LOG_FILE

# Check if training completed successfully
if [ ${PIPESTATUS[0]} -eq 0 ]; then
    echo "" | tee -a $LOG_FILE
    echo "Training completed successfully!" | tee -a $LOG_FILE
    echo "Model saved in: $OUTPUT_DIR" | tee -a $LOG_FILE
    
    # Run evaluation on test set
    echo "" | tee -a $LOG_FILE
    echo "Running evaluation on test set..." | tee -a $LOG_FILE
    
    python scripts/evaluation/evaluate_model.py \
        --model_path "${OUTPUT_DIR}/best_model.pth" \
        --dataset_path $DATASET_PATH \
        --model_name resunet_advanced \
        --use_tta \
        --output_dir $OUTPUT_DIR \
        2>&1 | tee -a $LOG_FILE
else
    echo "" | tee -a $LOG_FILE
    echo "Training failed!" | tee -a $LOG_FILE
    exit 1
fi

echo "" | tee -a $LOG_FILE
echo "=========================================="
echo "Advanced ResUNet Training Complete"
echo "=========================================="