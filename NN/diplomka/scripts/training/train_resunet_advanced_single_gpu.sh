#!/bin/bash

# Train Advanced ResUNet on single GPU with larger batch size
# Alternative to multi-GPU training when memory is limited

echo "=========================================="
echo "Training Advanced ResUNet (Single GPU)"
echo "=========================================="

# Set environment - use only GPU 0
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
# Go to project root directory
cd ../..
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Training parameters
DATASET_PATH="/data/prusek/training_big"
BATCH_SIZE=4  # Can use larger batch on single GPU
EPOCHS=100
PATIENCE=20
MIN_DELTA=1e-4

# Model specific parameters
MODEL_NAME="resunet_advanced"
LEARNING_RATE=5e-4
OPTIMIZER="adamw"

# Create output directory
OUTPUT_DIR="scripts/training/outputs/${MODEL_NAME}_single_gpu"
mkdir -p $OUTPUT_DIR

# Log file
LOG_FILE="${OUTPUT_DIR}/training.log"

echo "Configuration:" | tee $LOG_FILE
echo "- Model: Advanced ResUNet (Single GPU)" | tee -a $LOG_FILE
echo "- Dataset: $DATASET_PATH" | tee -a $LOG_FILE
echo "- Batch Size: $BATCH_SIZE" | tee -a $LOG_FILE
echo "- Learning Rate: $LEARNING_RATE" | tee -a $LOG_FILE
echo "- GPU: Single GPU (0)" | tee -a $LOG_FILE
echo "- Output Directory: $OUTPUT_DIR" | tee -a $LOG_FILE
echo "" | tee -a $LOG_FILE

echo "Starting training on single GPU..." | tee -a $LOG_FILE

# Run training on single GPU
python CNN_main_spheroid.py \
    --dataset_path $DATASET_PATH \
    --model resunet_advanced \
    --batch_size $BATCH_SIZE \
    --epochs $EPOCHS \
    --lr $LEARNING_RATE \
    --optimizer $OPTIMIZER \
    --patience $PATIENCE \
    --min_delta $MIN_DELTA \
    --use_instance_norm \
    --gpus 1 \
    --output_dir $OUTPUT_DIR \
    --boundary_weight 0.1 \
    2>&1 | tee -a $LOG_FILE

# Check if training completed successfully
if [ ${PIPESTATUS[0]} -eq 0 ]; then
    echo "" | tee -a $LOG_FILE
    echo "Training completed successfully!" | tee -a $LOG_FILE
    echo "Model saved in: $OUTPUT_DIR" | tee -a $LOG_FILE
    
    # Run evaluation on test set
    echo "" | tee -a $LOG_FILE
    echo "Running evaluation on test set..." | tee -a $LOG_FILE
    
    python scripts/evaluation/evaluate_model.py \
        --model_path "${OUTPUT_DIR}/best_model.pth" \
        --dataset_path $DATASET_PATH \
        --model_name resunet_advanced \
        --use_tta \
        --output_dir $OUTPUT_DIR \
        2>&1 | tee -a $LOG_FILE
else
    echo "" | tee -a $LOG_FILE
    echo "Training failed!" | tee -a $LOG_FILE
    exit 1
fi

echo "" | tee -a $LOG_FILE
echo "=========================================="
echo "Advanced ResUNet Training Complete"
echo "=========================================="