#!/bin/bash
# Adaptive training script with better learning rate and gradient accumulation

# Set environment variables
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
export CUDA_VISIBLE_DEVICES=0,1
export OMP_NUM_THREADS=8

# Clean up
echo "Cleaning up any hanging processes..."
pkill -f CNN_main_spheroid.py || true
sleep 2

echo "============================================================"
echo "ResUNet Adaptive Training on 2x NVIDIA L40S"
echo "============================================================"
echo ""

# Training with higher LR and OneCycle scheduler
echo "Starting training with adaptive learning rate..."
python ../../CNN_main_spheroid.py \
    --dataset_path /data/prusek/training_big \
    --output_dir ./outputs/resunet_spheroid_adaptive \
    --model resunet \
    --epochs 100 \
    --batch_size 8 \
    --lr 1e-3 \
    --weight_decay 1e-4 \
    --img_size 1024 \
    --optimizer adamw \
    --scheduler onecycle \
    --num_workers 4 \
    --patience 25 \
    --gpus 2 \
    --focal_weight 1.0 \
    --dice_weight 1.0 \
    --iou_weight 0.5 \
    --use_instance_norm \
    --min_delta 5e-4 \
    --use_cache

echo ""
echo "Training complete! Check outputs/resunet_spheroid_adaptive/ for results."