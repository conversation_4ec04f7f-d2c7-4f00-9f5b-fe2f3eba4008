#!/bin/bash
# Training script for ResUNet on spheroid dataset with 2x NVIDIA L40S

# Set environment variables for better CUDA memory management
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
export CUDA_VISIBLE_DEVICES=0,1

# Kill any hanging python processes
echo "Cleaning up any hanging processes..."
pkill -f CNN_main_spheroid.py || true
sleep 2

# First check single GPU training
echo "Testing with single GPU first..."
python ../../CNN_main_spheroid.py \
    --dataset_path /data/prusek/training_big \
    --output_dir ./outputs/resunet_test \
    --model resunet \
    --epochs 1 \
    --batch_size 4 \
    --lr 1e-5 \
    --optimizer adamw \
    --num_workers 4 \
    --gpus 1 \
    --use_instance_norm \
    --img_size 512

echo ""
echo "If single GPU test passed, running full training..."
echo ""

# Full training with conservative settings
python ../../CNN_main_spheroid.py \
    --dataset_path /data/prusek/training_big \
    --output_dir ./outputs/resunet_spheroid \
    --model resunet \
    --epochs 100 \
    --batch_size 6 \
    --lr 1e-5 \
    --weight_decay 1e-4 \
    --img_size 1024 \
    --optimizer adamw \
    --scheduler cosine \
    --num_workers 4 \
    --patience 20 \
    --gpus 2 \
    --focal_weight 1.0 \
    --dice_weight 1.0 \
    --iou_weight 0.5 \
    --use_instance_norm \
    --min_delta 1e-4