#!/bin/bash  
# Training script for PSPNet on spheroid dataset with 2x NVIDIA L40S

# PSPNet training with ResNet101 backbone
python ../../CNN_main_spheroid.py \
    --dataset_path /data/prusek/training_big \
    --output_dir ./outputs/pspnet_spheroid \
    --model pspnet \
    --pretrained \
    --epochs 100 \
    --batch_size 10 \
    --lr 1e-3 \
    --weight_decay 5e-4 \
    --img_size 1024 \
    --optimizer sgd \
    --scheduler reduce \
    --num_workers 8 \
    --patience 15 \
    --gpus 2 \
    --focal_weight 0.7 \
    --dice_weight 1.0 \
    --iou_weight 0.8 \
    --use_instance_norm