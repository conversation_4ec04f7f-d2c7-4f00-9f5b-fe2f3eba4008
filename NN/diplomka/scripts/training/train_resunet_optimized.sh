#!/bin/bash
# Training script for Optimized ResUNet (memory-efficient version)

# Dataset path - update this to your dataset location
DATASET_PATH="/path/to/training_big"

# Training configuration
MODEL="resunet_optimized"
BATCH_SIZE=2
IMG_SIZE=1024
EPOCHS=100
LR=1e-3
GPUS=2

# Create output directory with timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
OUTPUT_DIR="outputs/${MODEL}_${TIMESTAMP}"

echo "=========================================="
echo "Training Optimized ResUNet"
echo "=========================================="
echo "Model: ${MODEL}"
echo "Batch Size: ${BATCH_SIZE}"
echo "Image Size: ${IMG_SIZE}"
echo "GPUs: ${GPUS}"
echo "Output Dir: ${OUTPUT_DIR}"
echo "=========================================="

# Run training with recommended settings
python CNN_main_spheroid.py \
    --dataset_path "${DATASET_PATH}" \
    --output_dir "${OUTPUT_DIR}" \
    --model "${MODEL}" \
    --batch_size ${BATCH_SIZE} \
    --img_size ${IMG_SIZE} \
    --epochs ${EPOCHS} \
    --lr ${LR} \
    --gpus ${GPUS} \
    --optimizer adamw \
    --scheduler cosine \
    --weight_decay 1e-4 \
    --focal_weight 1.0 \
    --dice_weight 1.0 \
    --iou_weight 0.5 \
    --boundary_weight 0.1 \
    --use_instance_norm \
    --use_tta \
    --patience 20 \
    --min_delta 1e-4 \
    --num_workers 8 \
    --use_cache

# Optional: Add --use_checkpoint flag for even more memory savings
# --use_checkpoint \

echo "Training completed!"
echo "Results saved to: ${OUTPUT_DIR}"