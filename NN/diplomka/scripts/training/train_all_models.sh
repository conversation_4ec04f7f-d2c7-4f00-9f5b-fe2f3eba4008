#!/bin/bash
# Train all models with optimized settings

# Set environment variables
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
export CUDA_VISIBLE_DEVICES=0,1

echo "============================================================"
echo "Training all models on spheroid dataset"
echo "Dataset: /data/prusek/training_big"
echo "GPUs: 2x NVIDIA L40S"
echo "============================================================"

# Function to train a model
train_model() {
    MODEL=$1
    BATCH_SIZE=$2
    LR=$3
    
    echo ""
    echo "------------------------------------------------------------"
    echo "Training $MODEL with batch_size=$BATCH_SIZE, lr=$LR"
    echo "------------------------------------------------------------"
    
    python ../../CNN_main_spheroid.py \
        --dataset_path /data/prusek/training_big \
        --output_dir ./outputs/${MODEL}_spheroid \
        --model $MODEL \
        --epochs 100 \
        --batch_size $BATCH_SIZE \
        --lr $LR \
        --weight_decay 1e-4 \
        --img_size 1024 \
        --optimizer adamw \
        --scheduler cosine \
        --num_workers 4 \
        --patience 20 \
        --gpus 2 \
        --focal_weight 1.0 \
        --dice_weight 1.0 \
        --iou_weight 0.5 \
        --use_instance_norm \
        --min_delta 1e-4
}

# Train each model with optimized settings
train_model "resunet" 8 1e-5      # ResUNet - heavier model
train_model "hrnet" 10 1e-5       # HRNet - efficient architecture
train_model "pspnet" 12 1e-5      # PSPNet - lighter than ResUNet
train_model "transunet" 6 1e-5    # TransUNet - heaviest model

echo ""
echo "============================================================"
echo "All models trained! Check outputs/ for results."
echo "============================================================"