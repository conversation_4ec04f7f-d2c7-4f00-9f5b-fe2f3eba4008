#!/bin/bash
# Training script for HRNet on spheroid dataset with 2x NVIDIA L40S

# HRNet training with pretrained backbone
python ../../CNN_main_spheroid.py \
    --dataset_path /data/prusek/training_big \
    --output_dir ./outputs/hrnet_spheroid \
    --model hrnet \
    --pretrained \
    --epochs 100 \
    --batch_size 12 \
    --lr 5e-4 \
    --weight_decay 1e-4 \
    --img_size 1024 \
    --optimizer adamw \
    --scheduler onecycle \
    --num_workers 8 \
    --patience 15 \
    --min_delta 1e-4 \
    --gpus 2 \
    --focal_weight 0.8 \
    --dice_weight 1.2 \
    --iou_weight 0.5 \
    --use_instance_norm \
    --find_lr